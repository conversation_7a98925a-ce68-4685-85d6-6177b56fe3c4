#
# Table structure for table 'tx_landingpages_domain_model_flightroute'
#
CREATE TABLE tx_landingpages_domain_model_flightroute (
    uid int(11) NOT NULL auto_increment,
    pid int(11) DEFAULT '0' NOT NULL,

    origin_code varchar(10) DEFAULT '' NOT NULL,
    origin_name varchar(255) DEFAULT '' NOT NULL,
    origin_type varchar(20) DEFAULT 'airport' NOT NULL,
    destination_code varchar(10) DEFAULT '' NOT NULL,
    destination_name varchar(255) DEFAULT '' NOT NULL,
    destination_type varchar(20) DEFAULT 'airport' NOT NULL,
    route_slug varchar(255) DEFAULT '' NOT NULL,
    is_active tinyint(1) DEFAULT '1' NOT NULL,

    tstamp int(11) unsigned DEFAULT '0' NOT NULL,
    crdate int(11) unsigned DEFAULT '0' NOT NULL,
    cruser_id int(11) unsigned DEFAULT '0' NOT NULL,
    deleted tinyint(4) unsigned DEFAULT '0' NOT NULL,
    starttime int(11) unsigned DEFAULT '0' NOT NULL,
    endtime int(11) unsigned DEFAULT '0' NOT NULL,

    PRIMARY KEY (uid),
    KEY parent (pid),
    KEY route_slug (route_slug),
    KEY origin_destination (origin_code, destination_code),
    KEY active_routes (is_active, deleted)
);

#
# Extend pages table for new page types
#
CREATE TABLE pages (
    tx_landingpages_template_page int(11) DEFAULT '0' NOT NULL,
    tx_landingpages_url_pattern varchar(255) DEFAULT '' NOT NULL,
    tx_landingpages_route_prefix varchar(100) DEFAULT '' NOT NULL,
    tx_landingpages_cache_lifetime int(11) DEFAULT '3600' NOT NULL,
    tx_landingpages_enable_sitemap tinyint(1) DEFAULT '0' NOT NULL,
    tx_landingpages_template_mappings int(11) DEFAULT '0' NOT NULL,

    KEY idx_landing_template (tx_landingpages_template_page),
    KEY idx_landing_doktype (doktype)
);

#
# Table for storing generated page cache
#
CREATE TABLE tx_landingpages_page_cache (
    uid int(11) NOT NULL auto_increment,
    route_uid int(11) DEFAULT '0' NOT NULL,
    landing_page_uid int(11) DEFAULT '0' NOT NULL,
    cache_key varchar(255) DEFAULT '' NOT NULL,
    page_data mediumtext,
    created_at int(11) DEFAULT '0' NOT NULL,
    expires_at int(11) DEFAULT '0' NOT NULL,

    PRIMARY KEY (uid),
    KEY idx_cache_key (cache_key),
    KEY idx_expires (expires_at),
    KEY idx_route (route_uid),
    KEY idx_landing_page (landing_page_uid)
);

#
# Table for template mappings by route type combinations
#
CREATE TABLE tx_landingpages_domain_model_templatemapping (
    uid int(11) NOT NULL auto_increment,
    pid int(11) DEFAULT '0' NOT NULL,

    landing_page_uid int(11) DEFAULT '0' NOT NULL,
    origin_type varchar(20) DEFAULT '' NOT NULL,
    destination_type varchar(20) DEFAULT '' NOT NULL,
    template_page_uid int(11) DEFAULT '0' NOT NULL,

    tstamp int(11) unsigned DEFAULT '0' NOT NULL,
    crdate int(11) unsigned DEFAULT '0' NOT NULL,
    cruser_id int(11) unsigned DEFAULT '0' NOT NULL,
    deleted tinyint(4) unsigned DEFAULT '0' NOT NULL,
    hidden tinyint(4) unsigned DEFAULT '0' NOT NULL,

    PRIMARY KEY (uid),
    KEY parent (pid),
    KEY landing_page (landing_page_uid),
    KEY type_combination (origin_type, destination_type),
    UNIQUE KEY unique_mapping (landing_page_uid, origin_type, destination_type, deleted)
);

#
# Table structure for Flight From-To lists
#
CREATE TABLE tx_landingpages_domain_model_flightfromto (
    uid int(11) NOT NULL auto_increment,
    pid int(11) DEFAULT '0' NOT NULL,

    content_element_uid int(11) DEFAULT '0' NOT NULL,
    from_list text,
    to_list text,
    sorting int(11) DEFAULT '0' NOT NULL,

    tstamp int(11) unsigned DEFAULT '0' NOT NULL,
    crdate int(11) unsigned DEFAULT '0' NOT NULL,
    cruser_id int(11) unsigned DEFAULT '0' NOT NULL,
    deleted tinyint(4) unsigned DEFAULT '0' NOT NULL,

    PRIMARY KEY (uid),
    KEY parent (pid),
    KEY content_element (content_element_uid)
);




