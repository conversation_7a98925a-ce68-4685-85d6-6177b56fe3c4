<?php
declare(strict_types=1);

use Bgs\LandingPages\Preview\DestinationPairsMenuPreviewRenderer;
use TYPO3\CMS\Core\Utility\ExtensionManagementUtility;

defined('TYPO3') or die();

call_user_func(static function () {
    $gKey = 'landing-pages';
    $key = 'landingpages_destinationpairsmenu';

    // Register custom content element group for Flight Landing Pages
    $groupKey = 'flight_landing_pages';
    ExtensionManagementUtility::addTcaSelectItemGroup(
        'tt_content',
        'CType',
        $groupKey,
        "LLL:EXT:{$gKey}/Resources/Private/Language/locallang_db.xlf:tt_content.group.{$groupKey}",
        'after:special'
    );

    // Note: We use a custom content element instead of a plugin
    // This provides better integration with TYPO3's content element system

    // Add the Destination Pairs Menu content element to the "Type" dropdown
    ExtensionManagementUtility::addTcaSelectItem(
        'tt_content',
        'CType',
        [
            'label' => "LLL:EXT:{$gKey}/Resources/Private/Language/locallang_db.xlf:tt_content.CType.flight{$key}",
            'value' => $key,
            'icon' => 'content-destination-pairs-menu',
            'group' => $groupKey,
            'description' => "LLL:EXT:{$gKey}/Resources/Private/Language/locallang_db.xlf:tt_content.CType.flight{$key}.description",
        ],
        'header',
        'after'
    );

    // Configure the Destination Pairs Menu content element - similar to Header element
    $GLOBALS['TCA']['tt_content']['types'][$key] = [
        'showitem' => '
            --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:general,
                --palette--;;general,
                --palette--;;headers,
            --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:tabs.appearance,
                --palette--;;frames,
                --palette--;;appearanceLinks,
            --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:language,
                --palette--;;language,
            --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:access,
                --palette--;;hidden,
                --palette--;;access,
            --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:categories,
                categories,
            --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:notes,
                rowDescription,
            --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:extended,
        ',
        'columnsOverrides' => [
            'header' => [
                'description' => "LLL:EXT:{$gKey}/Resources/Private/Language/locallang_db.xlf:tt_content.header.description.destinationpairsmenu",
            ],
        ],
        'previewRenderer' => DestinationPairsMenuPreviewRenderer::class,
    ];

    // Content element is available on all pages but shows helpful warnings
    // in the backend preview when not used on Flight Landing Pages (doktype 201)

    // Add the Flight From-To 2 content element
    $key2 = 'landingpages_flightfromto2';

    ExtensionManagementUtility::addTcaSelectItem(
        'tt_content',
        'CType',
        [
            'label' => "LLL:EXT:{$gKey}/Resources/Private/Language/locallang_db.xlf:tt_content.CType.{$key2}",
            'value' => $key2,
            'icon' => 'content-flight-fromto',
            'group' => $groupKey,
            'description' => "LLL:EXT:{$gKey}/Resources/Private/Language/locallang_db.xlf:tt_content.CType.{$key2}.description",
        ],
        'header',
        'after'
    );

    // No custom fields needed - using FlexForm configuration

    // Configure the Flight From-To 2 content element with FlexForm
    $GLOBALS['TCA']['tt_content']['types'][$key2] = [
        'showitem' => '
            --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:general,
                --palette--;;general,
                --palette--;;headers,
                pi_flexform,
            --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:tabs.appearance,
                --palette--;;frames,
                --palette--;;appearanceLinks,
            --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:language,
                --palette--;;language,
            --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:access,
                --palette--;;hidden,
                --palette--;;access,
            --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:categories,
                categories,
            --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:notes,
                rowDescription,
            --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:extended,
        ',
        'columnsOverrides' => [
            'header' => [
                'description' => "LLL:EXT:{$gKey}/Resources/Private/Language/locallang_db.xlf:tt_content.header.description.flightfromto2",
            ],
        ],
    ];

    // Register FlexForm for the content element
    $GLOBALS['TCA']['tt_content']['columns']['pi_flexform']['config']['ds'][$key2] = "FILE:EXT:{$gKey}/Configuration/FlexForms/FlightFromTo.xml";
});
