<?xml version="1.0" encoding="utf-8" standalone="yes" ?>
<T3DataStructure>
    <meta>
        <langDisable>1</langDisable>
    </meta>
    <sheets>
        <sDEF>
            <ROOT>
                <TCEforms>
                    <sheetTitle>LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:flexform.flightfromto.sheet.general</sheetTitle>
                </TCEforms>
                <type>array</type>
                <el>
                    <from_list>
                        <TCEforms>
                            <label>LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:flexform.flightfromto.from_list</label>
                            <description>LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:flexform.flightfromto.from_list.description</description>
                            <config>
                                <type>text</type>
                                <renderType>textTable</renderType>
                                <cols>50</cols>
                                <rows>6</rows>
                                <wrap>virtual</wrap>
                                <placeholder>BER - Berlin Brandenburg Airport
MUC - Munich Airport
FRA - Frankfurt Airport
HAM - Hamburg Airport
DUS - Düsseldorf Airport</placeholder>
                            </config>
                        </TCEforms>
                    </from_list>
                    <to_list>
                        <TCEforms>
                            <label>LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:flexform.flightfromto.to_list</label>
                            <description>LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:flexform.flightfromto.to_list.description</description>
                            <config>
                                <type>text</type>
                                <renderType>textTable</renderType>
                                <cols>50</cols>
                                <rows>6</rows>
                                <wrap>virtual</wrap>
                                <placeholder>SOF - Sofia Airport
ATH - Athens Airport
BCN - Barcelona Airport
MAD - Madrid Airport
FCO - Rome Fiumicino Airport</placeholder>
                            </config>
                        </TCEforms>
                    </to_list>
                </el>
            </ROOT>
        </sDEF>
    </sheets>
</T3DataStructure>
