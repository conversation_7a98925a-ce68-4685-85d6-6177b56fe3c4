<?xml version="1.0" encoding="utf-8" standalone="yes" ?>
<T3DataStructure>
    <meta>
        <langDisable>1</langDisable>
    </meta>
    <sheets>
        <sDEF>
            <ROOT>
                <TCEforms>
                    <sheetTitle>LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:flexform.flightfromto.sheet.general</sheetTitle>
                </TCEforms>
                <type>array</type>
                <el>
                    <from_list>
                        <TCEforms>
                            <label>LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:flexform.flightfromto.from_list</label>
                            <description>LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:flexform.flightfromto.from_list.description</description>
                            <config>
                                <type>text</type>
                                <cols>50</cols>
                                <rows>6</rows>
                                <placeholder>BER - Berlin Brandenburg Airport&#10;MUC - Munich Airport&#10;FRA - Frankfurt Airport&#10;HAM - Hamburg Airport&#10;DUS - Düsseldorf Airport</placeholder>
                            </config>
                        </TCEforms>
                    </from_list>
                    <to_list>
                        <TCEforms>
                            <label>LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:flexform.flightfromto.to_list</label>
                            <description>LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:flexform.flightfromto.to_list.description</description>
                            <config>
                                <type>text</type>
                                <cols>50</cols>
                                <rows>6</rows>
                                <placeholder>SOF - Sofia Airport&#10;ATH - Athens Airport&#10;BCN - Barcelona Airport&#10;MAD - Madrid Airport&#10;FCO - Rome Fiumicino Airport</placeholder>
                            </config>
                        </TCEforms>
                    </to_list>
                </el>
            </ROOT>
        </sDEF>
    </sheets>
</T3DataStructure>
