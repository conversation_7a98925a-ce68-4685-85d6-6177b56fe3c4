<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" data-namespace-typo3-fluid="true">

<!-- FlightFromTo Plugin Template -->
<div class="flight-from-to-plugin">
    <p>TEST: FlightFromTo plugin is working!</p>
    <f:if condition="{hasAnyList}">
        <f:then>
            <div class="flight-from-to-container">
                <f:if condition="{hasFromList}">
                    <div class="flight-from-list">
                        <f:for each="{fromList}" as="item">
                            <div class="flight-item">from {item.code} - {item.name}</div>
                        </f:for>
                    </div>
                </f:if>

                <f:if condition="{hasToList}">
                    <div class="flight-to-list">
                        <f:for each="{toList}" as="item">
                            <div class="flight-item">to {item.code} - {item.name}</div>
                        </f:for>
                    </div>
                </f:if>
            </div>
        </f:then>
        <f:else>
            <div class="flight-from-to-empty">
                <p>No flight destinations configured. Please configure the plugin in the backend.</p>
            </div>
        </f:else>
    </f:if>
</div>

</html>
