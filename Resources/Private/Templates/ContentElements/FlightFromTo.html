<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" data-namespace-typo3-fluid="true">

<f:if condition="{data.header}">
    <f:render section="Header" arguments="{_all}" />
</f:if>

<f:if condition="{hasAnyList}">
    <f:then>
        <div class="flight-from-to-container">
            <f:if condition="{hasFromList}">
                <div class="flight-from-list">
                    <f:for each="{fromList}" as="item">
                        <div class="flight-item">from {item.code} - {item.name}</div>
                    </f:for>
                </div>
            </f:if>
            
            <f:if condition="{hasToList}">
                <div class="flight-to-list">
                    <f:for each="{toList}" as="item">
                        <div class="flight-item">to {item.code} - {item.name}</div>
                    </f:for>
                </div>
            </f:if>
        </div>
    </f:then>
    <f:else>
        <div class="flight-from-to-empty">
            <p>No flight destinations configured.</p>
        </div>
    </f:else>
</f:if>

<f:section name="Header">
    <f:switch expression="{data.header_layout}">
        <f:case value="1"><h1{f:if(condition: data.header_class, then: ' class="{data.header_class}"')}>{data.header}</h1></f:case>
        <f:case value="2"><h2{f:if(condition: data.header_class, then: ' class="{data.header_class}"')}>{data.header}</h2></f:case>
        <f:case value="3"><h3{f:if(condition: data.header_class, then: ' class="{data.header_class}"')}>{data.header}</h3></f:case>
        <f:case value="4"><h4{f:if(condition: data.header_class, then: ' class="{data.header_class}"')}>{data.header}</h4></f:case>
        <f:case value="5"><h5{f:if(condition: data.header_class, then: ' class="{data.header_class}"')}>{data.header}</h5></f:case>
        <f:case value="6"><h6{f:if(condition: data.header_class, then: ' class="{data.header_class}"')}>{data.header}</h6></f:case>
        <f:defaultCase><h2{f:if(condition: data.header_class, then: ' class="{data.header_class}"')}>{data.header}</h2></f:defaultCase>
    </f:switch>
</f:section>

</html>
