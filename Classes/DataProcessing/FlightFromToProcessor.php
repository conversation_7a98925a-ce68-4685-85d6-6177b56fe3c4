<?php
namespace Bgs\LandingPages\DataProcessing;

use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;
use TYPO3\CMS\Frontend\ContentObject\DataProcessorInterface;
use TYPO3\CMS\Core\Service\FlexFormService;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * DataProcessor for Flight From-To 2 content element
 * Processes simple lists of code-name pairs for from/to display
 */
class FlightFromToProcessor implements DataProcessorInterface
{
    /**
     * Process data for the Flight From-To 2 content element
     *
     * @param ContentObjectRenderer $cObj The content object renderer
     * @param array $contentObjectConfiguration The TypoScript configuration
     * @param array $processorConfiguration The processor configuration
     * @param array $processedData The processed data to be passed to the template
     * @return array The processed data with parsed from/to lists
     */
    public function process(
        ContentObjectRenderer $cObj,
        array $contentObjectConfiguration,
        array $processorConfiguration,
        array $processedData
    ): array {
        // Get FlexForm configuration
        $flexFormService = GeneralUtility::makeInstance(FlexFormService::class);
        $flexFormData = $flexFormService->convertFlexFormContentToArray($processedData['data']['pi_flexform'] ?? '');

        // Get the raw from and to lists from FlexForm
        $fromListRaw = $flexFormData['from_list'] ?? '';
        $toListRaw = $flexFormData['to_list'] ?? '';

        // Parse the from list
        $fromList = $this->parseCodeNameList($fromListRaw);
        
        // Parse the to list
        $toList = $this->parseCodeNameList($toListRaw);

        // Add parsed data to processed data array
        $processedData['fromList'] = $fromList;
        $processedData['toList'] = $toList;
        $processedData['hasFromList'] = !empty($fromList);
        $processedData['hasToList'] = !empty($toList);
        $processedData['hasAnyList'] = !empty($fromList) || !empty($toList);

        return $processedData;
    }

    /**
     * Parse a code-name list from textarea input
     * Expected format: "CODE - Name" per line
     *
     * @param string $listText Raw textarea content
     * @return array Array of parsed items with 'code' and 'name' keys
     */
    protected function parseCodeNameList(string $listText): array
    {
        $items = [];
        
        if (empty(trim($listText))) {
            return $items;
        }

        // Split by lines and process each line
        $lines = explode("\n", $listText);
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            // Skip empty lines
            if (empty($line)) {
                continue;
            }

            // Look for the pattern "CODE - Name"
            if (strpos($line, ' - ') !== false) {
                $parts = explode(' - ', $line, 2);
                $code = trim($parts[0]);
                $name = trim($parts[1]);
                
                if (!empty($code) && !empty($name)) {
                    $items[] = [
                        'code' => $code,
                        'name' => $name,
                        'display' => $code . ' - ' . $name
                    ];
                }
            } else {
                // If no dash separator found, treat the whole line as code
                $items[] = [
                    'code' => $line,
                    'name' => '',
                    'display' => $line
                ];
            }
        }

        return $items;
    }
}
