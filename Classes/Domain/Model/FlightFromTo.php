<?php
namespace Bgs\LandingPages\Domain\Model;

use TYPO3\CMS\Extbase\DomainObject\AbstractEntity;

/**
 * Flight From-To lists for content elements
 */
class FlightFromTo extends AbstractEntity
{
    protected int $contentElementUid = 0;
    protected string $fromList = '';
    protected string $toList = '';

    public function getContentElementUid(): int
    {
        return $this->contentElementUid;
    }

    public function setContentElementUid(int $contentElementUid): void
    {
        $this->contentElementUid = $contentElementUid;
    }

    public function getFromList(): string
    {
        return $this->fromList;
    }

    public function setFromList(string $fromList): void
    {
        $this->fromList = $fromList;
    }

    public function getToList(): string
    {
        return $this->toList;
    }

    public function setToList(string $toList): void
    {
        $this->toList = $toList;
    }
}
