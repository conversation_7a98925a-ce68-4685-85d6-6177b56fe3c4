<?php
namespace Bgs\LandingPages\Controller;

use TYPO3\CMS\Extbase\Mvc\Controller\ActionController;
use Bgs\LandingPages\Domain\Repository\FlightFromToRepository;

/**
 * Controller for FlightFromTo plugin
 */
class FlightFromToController extends ActionController
{
    protected FlightFromToRepository $flightFromToRepository;

    public function __construct(FlightFromToRepository $flightFromToRepository)
    {
        $this->flightFromToRepository = $flightFromToRepository;
    }

    /**
     * Show action - displays the from/to lists
     */
    public function showAction(): void
    {
        // Get the current content element UID
        $contentElementUid = (int)$this->configurationManager->getContentObject()->data['uid'];
        
        // Find the FlightFromTo record for this content element
        $flightFromTo = $this->flightFromToRepository->findByContentElementUid($contentElementUid);
        
        // Parse the lists
        $fromList = $this->parseCodeNameList($flightFromTo ? $flightFromTo->getFromList() : '');
        $toList = $this->parseCodeNameList($flightFromTo ? $flightFromTo->getToList() : '');
        
        // Assign to view
        $this->view->assign('fromList', $fromList);
        $this->view->assign('toList', $toList);
        $this->view->assign('hasFromList', !empty($fromList));
        $this->view->assign('hasToList', !empty($toList));
        $this->view->assign('hasAnyList', !empty($fromList) || !empty($toList));
    }

    /**
     * Parse a code-name list from textarea input
     * Expected format: "CODE - Name" per line
     *
     * @param string $listText Raw textarea content
     * @return array Array of parsed items with 'code' and 'name' keys
     */
    protected function parseCodeNameList(string $listText): array
    {
        $items = [];
        
        if (empty(trim($listText))) {
            return $items;
        }

        // Split by lines and process each line
        $lines = explode("\n", $listText);
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            // Skip empty lines
            if (empty($line)) {
                continue;
            }

            // Look for the pattern "CODE - Name"
            if (strpos($line, ' - ') !== false) {
                $parts = explode(' - ', $line, 2);
                $code = trim($parts[0]);
                $name = trim($parts[1]);
                
                if (!empty($code) && !empty($name)) {
                    $items[] = [
                        'code' => $code,
                        'name' => $name,
                        'display' => $code . ' - ' . $name
                    ];
                }
            } else {
                // If no dash separator found, treat the whole line as code
                $items[] = [
                    'code' => $line,
                    'name' => '',
                    'display' => $line
                ];
            }
        }

        return $items;
    }
}
